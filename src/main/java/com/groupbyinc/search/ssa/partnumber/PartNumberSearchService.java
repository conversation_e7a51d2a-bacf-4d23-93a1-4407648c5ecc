package com.groupbyinc.search.ssa.partnumber;

import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngine;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.request.RequestServed;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;

import jakarta.inject.Named;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.navigation.Navigation.mergeNavigationRefinements;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultSet;

import static io.micronaut.core.util.CollectionUtils.isEmpty;


@Slf4j
@Singleton
public class PartNumberSearchService {

    /**
     * The maximum number of records to return from the primary search engine when the expanded search is performed.
     */
    static final int PRIMARY_RESULTS_MAX_LIMIT = 100;

    private final SearchEngine primarySearchEngine;
    private final SearchEngine relevancySearchEngine;

    public PartNumberSearchService(@Named("mongoSearchEngine") SearchEngine primarySearchEngine,
                                   @Named("googleSearchEngine") SearchEngine relevancySearchEngine) {
        this.primarySearchEngine = primarySearchEngine;
        this.relevancySearchEngine = relevancySearchEngine;
    }

    /**
     * Search for part numbers attributes.
     * It can perform a fallback or expanded search using another search engine
     * depending on SearchParameters and primary search results.
     *
     * @param searchParameters the search parameters used to conduct a search.
     * @return empty {@link SearchResults} object if no records were found,
     * otherwise the result of search call enriched with metadata.
     */
    @Nonnull
    public SearchResults search(@Nonnull SearchParameters searchParameters) {
        if (isEmpty(searchParameters.getPartNumberSearchableAttributes())
            && !searchParameters.isPartNumberExpansionEnabled()) {
            return SearchResults.builder().build();
        }

        SearchResults primarySearchResults;
        try {
            // If part number expansion is enabled, we should always get up to PRIMARY_RESULTS_MAX_LIMIT records
            if (searchParameters.isPartNumberExpansionEnabled()) {
                var mongoEngineParams = searchParameters.toBuilder()
                    .pagination(new Pagination(PRIMARY_RESULTS_MAX_LIMIT, 0L))
                    .build();

                primarySearchResults = primarySearchEngine.search(mongoEngineParams);
            } else {
                primarySearchResults = primarySearchEngine.search(searchParameters);
            }
        } catch (Exception e) {
            var primarySearchEngineName = primarySearchEngine.getClass().getSimpleName();
            log.warn("{} error.", primarySearchEngineName, e);
            getRequestContext().addWarning("%s error: %s".formatted(primarySearchEngineName, e.getMessage()));
            if (isFallbackEnabled(searchParameters)) {
                return fallbackSearch(searchParameters, relevancySearchEngine);
            } else {
                throw new ProcessingException(e);
            }
        }

        if (primarySearchResults.hasNoRecords()
            && !primarySearchResults.isFallbackSearchEngineUsed()
            && isFallbackEnabled(searchParameters)) {
            log.info("Primary search engine yields no results.");
            return fallbackFromEmptyResult(searchParameters, primarySearchResults);
        }

        // At this point the primary search yields any results
        if (searchParameters.isPartNumberExpansionEnabled()) {

            var expandedSearchParameters = prepareExpandedSearchParameters(
                searchParameters,
                primarySearchResults
            );
            var expandedSearchResults = performExpandedSearch(expandedSearchParameters);

            var limitedPrimaryResults = limitPrimarySearchResults(searchParameters, primarySearchResults);
            addPartNumberLabelToRecords(limitedPrimaryResults);

            return mergeSearchResults(
                limitedPrimaryResults,
                expandedSearchResults,
                searchParameters.getPinnedRefinements(),
                searchParameters.getMongoFacetConverter().getNavigationComparator()
            );
        }

        return primarySearchResults;
    }

    /**
     * Add the {@link RecordLabel#PART_NUMBER} label to all records in the search results.
     * The PART_NUMBER label is added only if the record was not pinned.
     *
     * @param searchResults the search results to which the PART_NUMBER label will be added.
     */
    public void addPartNumberLabelToRecords(SearchResults searchResults) {
        if (searchResults == null || isEmpty(searchResults.getRecords())) {
            return;
        }
        var updatedRecords = searchResults.getRecords().stream()
            .map(record -> {
                var labels = Objects.requireNonNullElse(record.getLabels(), new LinkedHashSet<RecordLabel>());
                if (!labels.contains(RecordLabel.PINNED)) {
                    labels.add(RecordLabel.PART_NUMBER);
                    if (record.getLabel() == null) {
                        record = record.withLabel(RecordLabel.PART_NUMBER);
                    }
                    return record.withLabels(labels);
                }
                return record;
            })
            .toList();
        searchResults.setRecords(updatedRecords);
    }

    private static boolean isFallbackEnabled(SearchParameters searchParameters) {
        // expanded search will be invoked as a fallback if enabled
        return searchParameters.isPartNumberExpansionEnabled() || searchParameters.isPartNumberFallbackEnabled();
    }

    private SearchResults fallbackFromEmptyResult(SearchParameters searchParameters,
                                                  SearchResults primarySearchResults) {

        return isFallbackEnabled(searchParameters)
            ? fallbackSearch(searchParameters, relevancySearchEngine)
            : primarySearchResults;
    }

    private SearchResults fallbackSearch(SearchParameters searchParameters, SearchEngine fallbackEngine) {
        log.info("Secondary search engine is used for search: {}", fallbackEngine.getClass().getSimpleName());
        var fallbackSearchResults = fallbackEngine.search(searchParameters);
        fallbackSearchResults.setFallbackSearchEngineUsed(true);
        return fallbackSearchResults;
    }

    /**
     * Limits the number of primary search results to {@link #PRIMARY_RESULTS_MAX_LIMIT}
     * and applies the original pagination to the limited results.
     *
     * @param searchParameters     the original search parameters
     * @param primarySearchResults the primary search results
     * @return the limited and paginated primary search results
     */
    private SearchResults limitPrimarySearchResults(SearchParameters searchParameters,
                                                    SearchResults primarySearchResults) {
        var primarySearchResultsBuilder = primarySearchResults.toBuilder();

        if (primarySearchResults.getNumTotalRecords() > PRIMARY_RESULTS_MAX_LIMIT) {
            primarySearchResultsBuilder.numTotalRecords(PRIMARY_RESULTS_MAX_LIMIT);
        }

        // Get the original pagination parameters
        var skip = searchParameters.getPagination().getOffset();
        var pageSize = searchParameters.getPagination().getSize();

        var allRecords = new ArrayList<>(primarySearchResults.getRecords());
        if (allRecords.size() > PRIMARY_RESULTS_MAX_LIMIT) {
            allRecords = new ArrayList<>(allRecords.subList(0, PRIMARY_RESULTS_MAX_LIMIT));
        }

        var paginatedRecords = applyOriginalPagination(allRecords, skip, pageSize);

        primarySearchResultsBuilder.records(paginatedRecords);
        return primarySearchResultsBuilder.build();
    }

    private List<Record> applyOriginalPagination(List<Record> records, long originalSkip, int originalPageSize) {
        if (records.isEmpty() || originalSkip >= records.size()) {
            return List.of();
        }

        int fromIndex = (int) Math.min(originalSkip, records.size());
        int toIndex = Math.min(fromIndex + originalPageSize, records.size());

        return new ArrayList<>(records.subList(fromIndex, toIndex));
    }

    /**
     * Prepares search parameters for expanded relevancy search by:
     * <ol>
     *     <li>Excluding product IDs from primary results</li>
     *     <li>Adjusting pagination based on the primary search results</li>
     *     <li>Setting search mode to FACETED_SEARCH if necessary</li>
     * </ol>
     *
     * @param originalParams the original search parameters
     * @param primaryResults results from the primary search engine
     * @return adjusted search parameters for expanded search
     */
    private SearchParameters prepareExpandedSearchParameters(SearchParameters originalParams,
                                                             SearchResults primaryResults) {
        // Calculate new pagination
        int originalPageSize = originalParams.getPagination().getSize();
        long originalSkip = originalParams.getPagination().getOffset();
        long primaryResultsTotalCount = Math.min(primaryResults.getNumTotalRecords(), PRIMARY_RESULTS_MAX_LIMIT);

        int adjustedPageSize;
        long adjustedSkip;

        if (originalSkip < primaryResultsTotalCount) {
            long remainingNeeded = originalPageSize - (primaryResultsTotalCount - originalSkip);
            adjustedPageSize = (int) Math.max(0, remainingNeeded);
            adjustedSkip = 0;
        } else { // Original skip goes beyond primary results
            adjustedPageSize = originalPageSize;
            adjustedSkip = originalSkip - primaryResultsTotalCount;
        }

        var paramsBuilder = originalParams.toBuilder();
        paramsBuilder.pagination(new Pagination(adjustedPageSize, adjustedSkip));

        // Exclude primary product IDs
        Set<String> primaryProductIds = primaryResults.getRecords().stream()
            .map(Record::getProductId)
            .collect(Collectors.toSet());

        List<String> excludedProductIds = new ArrayList<>();
        if (originalParams.getProductIdFilter() != null) {
            excludedProductIds.addAll(originalParams.getProductIdFilter().excludedProductIds());
        }
        excludedProductIds.addAll(primaryProductIds);

        paramsBuilder.productIdFilter(new ProductIdFilter(
            originalParams.getProductIdFilter() != null ?
                originalParams.getProductIdFilter().includedProductIds() :
                null,
            excludedProductIds
        ));

        if (adjustedPageSize == 0) {
            paramsBuilder.searchMode(SearchMode.FACETED_SEARCH);
        }

        return paramsBuilder.build();
    }

    private SearchResults performExpandedSearch(SearchParameters expandedSearchParameters) {
        log.info("Search expansion is enabled. Relevancy search engine is used for search: {}",
            relevancySearchEngine.getClass().getSimpleName()
        );
        return relevancySearchEngine.search(expandedSearchParameters);
    }

    /**
     * Merges search results from primary and expanded searches according to the following rules:
     * <ul>
     *   <li>Primary results come first, maintaining their order</li>
     *   <li>Expanded results follow, maintaining their order</li>
     *   <li>Facets merging rules:
     *      <ul>
     *          <li>If the expanded search results have {@code numTotalRecords==0}, use primary facets</li>
     *          <li>Else merge facets with comprehensive refinement value merging:
     *              <ul>
     *                  <li>Expanded search navigations come first, preserving their order</li>
     *                  <li>For common navigations, merge refinements and sum their counts</li>
     *                  <li>Preserve expanded search refinement order for common refinements</li>
     *                  <li>Append unique refinements from primary search for each navigation</li>
     *                  <li>Add primary search navigations after expanded search navigations</li>
     *              </ul>
     *          </li>
     *      </ul>
     *   </li>
     * </ul>
     *
     * @param primaryResults       results from primary search
     * @param expandedResults      results from expanded search
     * @param pinnedRefinements    pinned refinements
     * @param navigationComparator comparator for sorting navigations
     * @return merged search results
     */
    private SearchResults mergeSearchResults(SearchResults primaryResults,
                                             SearchResults expandedResults,
                                             List<PinnedRefinement> pinnedRefinements,
                                             Comparator<Navigation> navigationComparator) {
        var resultBuilder = primaryResults.toBuilder();

        var primaryResultIds = primaryResults.getRecords().stream()
            .map(Record::getProductId)
            .collect(Collectors.toSet());
        var mergedRecords = new ArrayList<>(primaryResults.getRecords());

        // Expanded results should be filtered to exclude primary records,
        // this is more of a safety measure because expanded search should contain filter by primary records IDs
        expandedResults.getRecords().stream()
            .filter(r -> !primaryResultIds.contains(r.getProductId()))
            .forEach(mergedRecords::add);
        resultBuilder.records(mergedRecords);

        resultBuilder.numTotalRecords(primaryResults.getNumTotalRecords() + expandedResults.getNumTotalRecords());

        if (expandedResults.isNoResults()) {
            resultBuilder.navigations(primaryResults.getNavigations());
        } else {
            var expandedNavigationsByField = expandedResults.getNavigations().stream()
                .collect(Collectors.toMap(Navigation::getField, nav -> nav));

            var primaryNavigationsByField = primaryResults.getNavigations().stream()
                .collect(Collectors.toMap(Navigation::getField, nav -> nav));

            var mergedNavigations = new ArrayList<Navigation>();

            // Process expanded search navigations first (they have priority)
            for (var expandedNav : expandedResults.getNavigations()) {
                var navField = expandedNav.getField();

                if (primaryNavigationsByField.containsKey(navField)) {
                    // This is a common navigation - merge the refinements
                    var primaryNav = primaryNavigationsByField.get(navField);
                    mergedNavigations.add(
                        mergeNavigationRefinements(expandedNav, primaryNav, pinnedRefinements)
                    );
                } else {
                    mergedNavigations.add(expandedNav);
                }
            }

            // Add primary search navigations (those not present in expanded search results)
            for (var primaryNav : primaryResults.getNavigations()) {
                if (!expandedNavigationsByField.containsKey(primaryNav.getField())) {
                    mergedNavigations.add(primaryNav);
                }
            }

            mergedNavigations.sort(navigationComparator);
            resultBuilder.navigations(mergedNavigations);
        }

        return resultBuilder.requestServed(RequestServed.MONGO_GOOGLE).build();
    }
}
